// https://nuxt.com/docs/api/configuration/nuxt-config
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'

// 获取环境变量或使用默认值
const apiBase = process.env.NUXT_PUBLIC_API_BASE || 'https://api.dinq.io'
const siteUrl = process.env.NUXT_PUBLIC_SITE_URL || 'https://dinq.io'

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxt/image', '@unocss/nuxt', 'motion-v/nuxt'],
  builder: 'vite',
    plugins: ['~/plugins/globals.js'],

  // Global CSS
  css: [
    '~/assets/css/fonts.css',
    '~/assets/styles/global.css'
  ],

  // Runtime config, accessible in the app via useRuntimeConfig()
  runtimeConfig: {
    // Public variables, available on both client and server side
    public: {
      apiBase,
      siteUrl,
      // Prerender mode flag, used to simulate API responses during prerendering
      // In hybrid rendering mode, only the actual prerender phase needs simulation
      isPrerenderMode: false, // Disable prerender simulation because we now use hybrid rendering
    }
  },

  // Restore hybrid rendering mode - this is the core requirement for SEO and social sharing
  nitro: {
    preset: 'cloudflare-pages',
    output: {
      dir: '.output/public',
      publicDir: '.output/public'
    },
    prerender: {
      routes: ['/'],
      crawlLinks: false,
      failOnError: false
    }
  },

  routeRules: {
    // 首页预渲染
    '/': { prerender: true },

    // Redirect /talent-pool to /search for backward compatibility
    '/talent-pool': { redirect: '/search' },

    // GitHub 和 Compare 页面使用 SSR - 这是关键！
    '/github/**': { prerender: false },
    '/github_compare': { prerender: false }, // GitHub比较页面的新路径
    '/compare': { prerender: false },    // 修复：Scholar比较页面的正确路径
    '/compare/**': { prerender: false }, // 保留：以防有子路径

    // Scholar 分析页面使用 SSR - 新增！
    '/report/**': { prerender: false },

    // 调试页面不预渲染
    '/debug-meta': { prerender: false },

    // API proxy routes - solve CORS issues
    '/api/v1/graph': { proxy: 'https://search.dinq.io/api/v1/graph' },
    '/api/v1/recommend': { proxy: 'https://search.dinq.io/api/v1/recommend' },
    '/api/v1/talent/search': { proxy: 'https://search.dinq.io/api/v1/talent/search' },
    '/api/v1/talent/network': { proxy: 'https://search.dinq.io/api/v1/talent/network' },
    '/api/v1/talent/email': { proxy: 'https://search.dinq.io/api/v1/talent/email' },
    '/api/v1/talent/credits': { proxy: 'https://search.dinq.io/api/v1/talent/credits' },
    '/api/v1/favorite/**': { proxy: 'https://search.dinq.io/api/v1/favorite/**' },

    // Other API routes
    '/api/**': { prerender: false }
  },

  vite:{
    // Expose environment variables to client
    define: {
      'import.meta.env.NUXT_PUBLIC_API_BASE': JSON.stringify(apiBase)
    },
    plugins: [
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'assets/svg')],
        symbolId: 'icon-[dir]-[name]',
      }),
    ],
    server:{
      cors: true,
      proxy: {
        '/api/v1': {
          target: 'https://search.dinq.io',
          changeOrigin: true}
      }
    }
  }
})