<template>
  <div
    class="responsive-grid overflow-y-auto max-h-[88vh] min-h-[80vh] content-start"
  >
    <div
      v-for="(item, index) in mainList"
      :key="item.id"
      class="transition-all duration-150 w-full"
      :class="[ 
        item.type === 'folder' ? 'folder-box' : 'card-box',
        draggingItem?.id !== item.id &&
        item.id === hoveringItemId &&
        draggingItem?.type === 'card' &&
        item.type === 'folder' &&
        !canSwap
          ? 'scale-90'
          : '',
      ]"
      draggable="true"
      @dragstart="e => onDragStart(item, index, e)"
      @dragenter="onDragEnter(item, index)"
      @dragleave="onDragLeave"
      @dragover.prevent
      @drop.prevent="onDrop(item, index)"
    >
      <slot :item="item" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
const draggingItem = ref(null)
const draggingIndex = ref(-1)
const hoveringItemId = ref(null)
const hoverTimer = ref(null)
const canSwap = ref(false)

const props = defineProps({
  modelValue: Array,
})

const emit = defineEmits(['update:modelValue', 'move-to-folder', 'reorder-items'])

const mainList = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function onDragStart(item, index, event) {
  draggingItem.value = item
  draggingIndex.value = index
  canSwap.value = false

  // 自定义拖拽镜像
  const dragGhost = event.target.cloneNode(true)
  
  // 确保拖拽镜像有正确的尺寸
  dragGhost.style.width = '295px'
  dragGhost.style.height = '215px'
  dragGhost.style.minWidth = '295px'
  dragGhost.style.minHeight = '215px'
  dragGhost.style.maxWidth = '295px'
  dragGhost.style.transform = 'scale(0.8)'
  dragGhost.style.opacity = '0.7'
  dragGhost.style.position = 'absolute'
  dragGhost.style.top = '-9999px'
  dragGhost.style.left = '-9999px'
  dragGhost.style.pointerEvents = 'none'
  
  // 保持文件夹背景图片和样式
  if (item.type === 'folder') {
    // 复制原始元素的计算样式，特别是背景相关的
    const originalElement = event.target
    const computedStyle = window.getComputedStyle(originalElement)
    
    // 复制背景相关样式
    dragGhost.style.backgroundImage = computedStyle.backgroundImage
    dragGhost.style.backgroundSize = computedStyle.backgroundSize
    dragGhost.style.backgroundRepeat = computedStyle.backgroundRepeat
    dragGhost.style.backgroundPosition = computedStyle.backgroundPosition
    dragGhost.style.backgroundColor = 'transparent'
    dragGhost.style.border = 'none'
    dragGhost.style.borderRadius = '10px'
    
    // 确保文件夹样式类保持
    dragGhost.className = originalElement.className
  } else {
    // 卡片保持边框和背景，适应深色模式
    const isDark = document.documentElement.classList.contains('dark')
    dragGhost.style.border = '2px solid #CB7C5D'
    dragGhost.style.borderRadius = '8px'
    dragGhost.style.backgroundColor = isDark ? '#242425' : 'white'
    dragGhost.style.color = isDark ? 'white' : '#1f2937'
    
    // 确保卡片样式类保持
    dragGhost.className = event.target.className
  }
  
  document.body.appendChild(dragGhost)
  event.dataTransfer.setDragImage(dragGhost, 150, 100)
  setTimeout(() => {
    document.body.removeChild(dragGhost)
  }, 0)
}

function onDragEnter(targetItem, targetIndex) {
  if (!draggingItem.value || targetItem.id === draggingItem.value.id) return

  hoveringItemId.value = targetItem.id
  clearTimeout(hoverTimer.value)
  canSwap.value = false

  if (targetItem.id === draggingItem.value.id) return

  if (draggingItem.value.type === 'card' && targetItem.type === 'folder') {
    hoverTimer.value = setTimeout(() => {
      canSwap.value = true
    }, 2000)
  } else {
    canSwap.value = true
  }
}

function onDragLeave() {
  hoveringItemId.value = null
  clearTimeout(hoverTimer.value)
  canSwap.value = false
}

function onDrop(targetItem, targetIndex) {
  clearTimeout(hoverTimer.value)

  const dragged = draggingItem.value
  const fromIndex = draggingIndex.value

  if (!dragged || dragged.id === targetItem.id) return

  if (targetItem.type === 'folder' && dragged.type === 'card') {
    if (!canSwap.value) {
      // 触发API调用事件，让父组件处理
      emit('move-to-folder', {
        card: dragged,
        targetFolder: targetItem
      })
    } else {
      // 排序操作：触发API调用事件
      emit('reorder-items', {
        draggedItem: dragged,
        fromIndex,
        targetIndex
      })
    }
  } else {
    // 排序操作：触发API调用事件
    emit('reorder-items', {
      draggedItem: dragged,
      fromIndex,
      targetIndex
    })
  }

  resetDrag()
}


function moveItem(from, to) {
  const list = [...mainList.value]
  const item = list.splice(from, 1)[0]
  list.splice(to, 0, item)
  mainList.value = list
}

function removeFromList(id) {
  const list = [...mainList.value]
  const index = list.findIndex(i => i.id === id)
  if (index !== -1) {
    list.splice(index, 1)
    mainList.value = list
  }
}

function resetDrag() {
  draggingItem.value = null
  draggingIndex.value = -1
  hoveringItemId.value = null
  canSwap.value = false
  clearTimeout(hoverTimer.value)
}
</script>

<style scoped>
  /* 响应式网格布局 - 自适应间距填满容器 */
  .responsive-grid {
    display: grid;
    scroll-behavior: smooth;

    /* 滚动区域的内边距 */
    padding: 16px 24px 48px 24px;

    /* 移动端 (<768px): 单列，自适应宽度 */
    grid-template-columns: 1fr;
    gap: 12px;
    justify-content: center;

    /* 小平板端: 2列，自适应间距 (940px - 10px = 930px) */
    @media (min-width: 930px) {
      grid-template-columns: repeat(2, minmax(295px, 1fr));
      gap: clamp(16px, 3vw, 40px);
      justify-content: stretch;
    }

    /* 中等平板: 3列，自适应间距 (1393px - 20px = 1373px) */
    @media (min-width: 1373px) {
      grid-template-columns: repeat(3, minmax(295px, 1fr));
      gap: clamp(20px, 2.5vw, 35px);
      justify-content: stretch;
    }

    /* 桌面端: 4列，自适应间距 (1664px - 30px = 1634px) */
    @media (min-width: 1634px) {
      grid-template-columns: repeat(4, minmax(295px, 1fr));
      gap: clamp(24px, 2vw, 32px);
      justify-content: stretch;
    }

    /* 大屏桌面: 5列，自适应间距 (2041px - 40px = 2001px) */
    @media (min-width: 2001px) {
      grid-template-columns: repeat(5, minmax(295px, 1fr));
      gap: clamp(28px, 1.8vw, 36px);
      justify-content: stretch;
    }

    /* 超大屏: 6列，自适应间距 (2414px - 50px = 2364px) */
    @media (min-width: 2364px) {
      grid-template-columns: repeat(6, minmax(295px, 1fr));
      gap: clamp(32px, 1.5vw, 40px);
      justify-content: stretch;
    }
  }

  /* 侧边栏折叠状态下的调整 - 更早触发多列布局 */
  .sidebar-collapsed .responsive-grid {
    /* 小平板端: 2列 (930px - 192px = 738px) */
    @media (min-width: 738px) {
      grid-template-columns: repeat(2, minmax(295px, 1fr));
      gap: clamp(16px, 3vw, 40px);
      justify-content: stretch;
    }

    /* 中等平板: 3列 (1373px - 192px = 1181px) */
    @media (min-width: 1181px) {
      grid-template-columns: repeat(3, minmax(295px, 1fr));
      gap: clamp(20px, 2.5vw, 35px);
      justify-content: stretch;
    }

    /* 桌面端: 4列 (1634px - 192px = 1442px) */
    @media (min-width: 1442px) {
      grid-template-columns: repeat(4, minmax(295px, 1fr));
      gap: clamp(24px, 2vw, 32px);
      justify-content: stretch;
    }

    /* 大屏桌面: 5列 (2001px - 192px = 1809px) */
    @media (min-width: 1809px) {
      grid-template-columns: repeat(5, minmax(295px, 1fr));
      gap: clamp(28px, 1.8vw, 36px);
      justify-content: stretch;
    }

    /* 超大屏: 6列 (2364px - 192px = 2172px) */
    @media (min-width: 2172px) {
      grid-template-columns: repeat(6, minmax(295px, 1fr));
      gap: clamp(32px, 1.5vw, 40px);
      justify-content: stretch;
    }
  }

  .folder-box,
  .card-box {
    min-height: 215px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    transition:
      transform 0.2s ease,
      opacity 0.2s ease;

    /* 确保卡片在网格中正确显示 */
    width: 100%;
  }

  .scale-90 {
    transform: scale(0.9);
  }

</style>
