<template>
  <div
    class="responsive-grid overflow-y-auto max-h-[88vh] min-h-[80vh] content-start"
  >
    <div
      v-for="(item, index) in mainList"
      :key="item.id"
      class="transition-all duration-150 w-full"
      :class="[ 
        item.type === 'folder' ? 'folder-box' : 'card-box',
        draggingItem?.id !== item.id &&
        item.id === hoveringItemId &&
        draggingItem?.type === 'card' &&
        item.type === 'folder' &&
        !canSwap
          ? 'scale-90'
          : '',
      ]"
      draggable="true"
      @dragstart="e => onDragStart(item, index, e)"
      @dragenter="onDragEnter(item, index)"
      @dragleave="onDragLeave"
      @dragover.prevent
      @drop.prevent="onDrop(item, index)"
    >
      <slot :item="item" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
const draggingItem = ref(null)
const draggingIndex = ref(-1)
const hoveringItemId = ref(null)
const hoverTimer = ref(null)
const canSwap = ref(false)

const props = defineProps({
  modelValue: Array,
})

const emit = defineEmits(['update:modelValue', 'move-to-folder', 'reorder-items'])

const mainList = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function onDragStart(item, index, event) {
  draggingItem.value = item
  draggingIndex.value = index
  canSwap.value = false

  // 自定义拖拽镜像
  const dragGhost = event.target.cloneNode(true)
  
  // 确保拖拽镜像有正确的尺寸
  dragGhost.style.width = '295px'
  dragGhost.style.height = '215px'
  dragGhost.style.minWidth = '295px'
  dragGhost.style.minHeight = '215px'
  dragGhost.style.maxWidth = '295px'
  dragGhost.style.transform = 'scale(0.8)'
  dragGhost.style.opacity = '0.7'
  dragGhost.style.position = 'absolute'
  dragGhost.style.top = '-9999px'
  dragGhost.style.left = '-9999px'
  dragGhost.style.pointerEvents = 'none'
  
  // 保持文件夹背景图片和样式
  if (item.type === 'folder') {
    // 复制原始元素的计算样式，特别是背景相关的
    const originalElement = event.target
    const computedStyle = window.getComputedStyle(originalElement)
    
    // 复制背景相关样式
    dragGhost.style.backgroundImage = computedStyle.backgroundImage
    dragGhost.style.backgroundSize = computedStyle.backgroundSize
    dragGhost.style.backgroundRepeat = computedStyle.backgroundRepeat
    dragGhost.style.backgroundPosition = computedStyle.backgroundPosition
    dragGhost.style.backgroundColor = 'transparent'
    dragGhost.style.border = 'none'
    dragGhost.style.borderRadius = '10px'
    
    // 确保文件夹样式类保持
    dragGhost.className = originalElement.className
  } else {
    // 卡片保持边框和背景，适应深色模式
    const isDark = document.documentElement.classList.contains('dark')
    dragGhost.style.border = '2px solid #CB7C5D'
    dragGhost.style.borderRadius = '8px'
    dragGhost.style.backgroundColor = isDark ? '#242425' : 'white'
    dragGhost.style.color = isDark ? 'white' : '#1f2937'
    
    // 确保卡片样式类保持
    dragGhost.className = event.target.className
  }
  
  document.body.appendChild(dragGhost)
  event.dataTransfer.setDragImage(dragGhost, 150, 100)
  setTimeout(() => {
    document.body.removeChild(dragGhost)
  }, 0)
}

function onDragEnter(targetItem, targetIndex) {
  if (!draggingItem.value || targetItem.id === draggingItem.value.id) return

  hoveringItemId.value = targetItem.id
  clearTimeout(hoverTimer.value)
  canSwap.value = false

  if (targetItem.id === draggingItem.value.id) return

  if (draggingItem.value.type === 'card' && targetItem.type === 'folder') {
    hoverTimer.value = setTimeout(() => {
      canSwap.value = true
    }, 2000)
  } else {
    canSwap.value = true
  }
}

function onDragLeave() {
  hoveringItemId.value = null
  clearTimeout(hoverTimer.value)
  canSwap.value = false
}

function onDrop(targetItem, targetIndex) {
  clearTimeout(hoverTimer.value)

  const dragged = draggingItem.value
  const fromIndex = draggingIndex.value

  if (!dragged || dragged.id === targetItem.id) return

  if (targetItem.type === 'folder' && dragged.type === 'card') {
    if (!canSwap.value) {
      // 触发API调用事件，让父组件处理
      emit('move-to-folder', {
        card: dragged,
        targetFolder: targetItem
      })
    } else {
      // 排序操作：触发API调用事件
      emit('reorder-items', {
        draggedItem: dragged,
        fromIndex,
        targetIndex
      })
    }
  } else {
    // 排序操作：触发API调用事件
    emit('reorder-items', {
      draggedItem: dragged,
      fromIndex,
      targetIndex
    })
  }

  resetDrag()
}


function moveItem(from, to) {
  const list = [...mainList.value]
  const item = list.splice(from, 1)[0]
  list.splice(to, 0, item)
  mainList.value = list
}

function removeFromList(id) {
  const list = [...mainList.value]
  const index = list.findIndex(i => i.id === id)
  if (index !== -1) {
    list.splice(index, 1)
    mainList.value = list
  }
}

function resetDrag() {
  draggingItem.value = null
  draggingIndex.value = -1
  hoveringItemId.value = null
  canSwap.value = false
  clearTimeout(hoverTimer.value)
}
</script>

<style scoped>
  /* 响应式网格布局 - 左对齐固定列数，考虑侧边栏宽度 */
  .responsive-grid {
    display: grid;
    scroll-behavior: smooth;

    /* 滚动区域的内边距 */
    padding: 16px 24px 48px 24px;

    /* 移动端 (<768px): 单列，295px卡片，12px间距
       侧边栏隐藏，全宽可用 */
    grid-template-columns: 295px;
    gap: 12px;
    justify-content: start;

    /* 小平板端 (768px+): 2列固定，310px卡片，16px间距
       需要: 2×310 + 1×16 + 256(sidebar) + 48(padding) = 940px */
    @media (min-width: 940px) {
      grid-template-columns: repeat(2, 310px);
      gap: 16px;
      justify-content: start;
    }

    /* 中等平板 (1024px+): 3列固定，315px卡片，22px间距
       需要: 3×315 + 2×22 + 256(sidebar) + 48(padding) = 1393px */
    @media (min-width: 1393px) {
      grid-template-columns: repeat(3, 315px);
      gap: 22px;
      justify-content: start;
    }

    /* 桌面端 (1200px+): 4列固定，320px卡片，24px间距
       需要: 4×320 + 3×24 + 256(sidebar) + 48(padding) = 1664px */
    @media (min-width: 1664px) {
      grid-template-columns: repeat(4, 320px);
      gap: 24px;
      justify-content: start;
    }

    /* 大屏桌面 (1400px+): 5列固定，325px卡片，28px间距
       需要: 5×325 + 4×28 + 256(sidebar) + 48(padding) = 2041px */
    @media (min-width: 2041px) {
      grid-template-columns: repeat(5, 325px);
      gap: 28px;
      justify-content: start;
    }

    /* 超大屏 (1800px+): 6列固定，325px卡片，32px间距
       需要: 6×325 + 5×32 + 256(sidebar) + 48(padding) = 2414px */
    @media (min-width: 2414px) {
      grid-template-columns: repeat(6, 325px);
      gap: 32px;
      justify-content: start;
    }
  }

  /* 侧边栏折叠状态下的调整 - 减少192px (256-64) */
  .sidebar-collapsed .responsive-grid {
    /* 小平板端: 2列 - 减少192px = 748px */
    @media (min-width: 748px) {
      grid-template-columns: repeat(2, 310px);
      gap: 16px;
      justify-content: start;
    }

    /* 中等平板: 3列 - 减少192px = 1201px */
    @media (min-width: 1201px) {
      grid-template-columns: repeat(3, 315px);
      gap: 22px;
      justify-content: start;
    }

    /* 桌面端: 4列 - 减少192px = 1472px */
    @media (min-width: 1472px) {
      grid-template-columns: repeat(4, 320px);
      gap: 24px;
      justify-content: start;
    }

    /* 大屏桌面: 5列 - 减少192px = 1849px */
    @media (min-width: 1849px) {
      grid-template-columns: repeat(5, 325px);
      gap: 28px;
      justify-content: start;
    }

    /* 超大屏: 6列 - 减少192px = 2222px */
    @media (min-width: 2222px) {
      grid-template-columns: repeat(6, 325px);
      gap: 32px;
      justify-content: start;
    }
  }

  .folder-box,
  .card-box {
    min-height: 215px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    transition:
      transform 0.2s ease,
      opacity 0.2s ease;

    /* 确保卡片在网格中正确显示 */
    width: 100%;
  }

  .scale-90 {
    transform: scale(0.9);
  }

</style>
