<template>
  <div
    class="min-h-screen py-4 transition-colors duration-300 dark:text-white text-gray-900"
    :class="{ 'pt-15': isMobile }"
  >
    <div class="talent-title px-6">
      <div class="flex items-center mb-4 h-10">
        <button
          v-if="currentFolder"
          @click="exitFolder"
          class="mr-2 text-gray-500 hover:text-black dark:hover:text-white bg-transparent"
        >
          <img src="~/assets/image/Left.svg" alt="" class="w-6 h-6" />
        </button>

        <!-- talents title -->
        <template v-if="currentFolder">
          <input
            v-if="currentFolder.editing"
            v-model="currentFolder.name"
            @blur="onFolderInputBlur(currentFolder)"
            @keydown.enter="onFolderInputEnter(currentFolder, $event)"
            @keydown.esc="cancelFolderRename(currentFolder)"
            class="text-xl font-semibold bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-64 folder-rename-input"
          />
          <h2
            v-else
            class="text-xl font-semibold flex-1 cursor-pointer hover:underline"
            @click="editFolderName(currentFolder)"
          >
            {{ currentFolder.name || 'Unnamed Folder' }}
          </h2>
        </template>

        <h2 v-else class="text-xl font-semibold flex-1">Talents</h2>
      </div>

      <div class="fx-cer gap-4">
        <!-- New Folder -->
        <button
          v-if="!currentFolder"
          @click="addFolder"
          class="text-3 fx-cer w-[120px] h-[32px] gap-2 border border-black/50 rounded-1 px-3 py-1 rounded bg-transparent text-black dark:( text-gray-400 border-gray-400)"
        >
          <img src="~/assets/image/add-folder 1.svg" alt="" class="btn-icon btn-icon-light" />
          <img src="~/assets/image/add-folder 2.svg" alt="" class="btn-icon btn-icon-dark" />
          New Folder
        </button>
        <!-- upload -->
        <button
          v-if="!currentFolder"
          @click="props.onGoToUpload"
          disabled
          class="text-3 fx-cer justify-center w-[120px] h-[32px] gap-2 border border-black/50 rounded-1 px-3 py-1 rounded bg-transparent text-black dark:( text-gray-400 border-gray-400) opacity-50 cursor-not-allowed relative group"
        >
          <img src="~/assets/image/upload1.svg" alt="" class="btn-icon btn-icon-light opacity-50" />
          <img src="~/assets/image/upload2.svg" alt="" class="btn-icon btn-icon-dark opacity-50" />
          Upload
          <!-- Tooltip -->
          <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            Coming Soon
          </div>
        </button>
      </div>
    </div>

    <!-- 加载状态 - 骨架屏 -->
    <div v-if="loading" class="skeleton-grid mt-4">
      <!-- 显示多个骨架卡片 -->
      <div
        v-for="n in 8"
        :key="n"
        class="skeleton-card"
        :style="{ animationDelay: `${(n - 1) * 0.1}s` }"
      >
        <!-- 顶部角标骨架 -->
        <div class="absolute top-0 left-0 w-16 h-6 rounded-tl-lg rounded-br-lg skeleton-loading"></div>

        <!-- 右上角星标骨架 -->
        <div class="absolute right-2 top-2 w-8 h-8 rounded-2 skeleton-loading"></div>

        <!-- 主要内容区域 -->
        <div class="flex flex-col gap-3 items-start mt-2">
          <!-- 头像和基本信息骨架 -->
          <div class="flex items-center gap-2 justify-start w-full">
            <!-- 头像骨架 -->
            <div class="w-12 h-12 rounded-full skeleton-loading"></div>

            <!-- 文字信息骨架 -->
            <div class="text-left text-sm max-w-[180px] flex-1">
              <!-- 姓名骨架 -->
              <div class="h-4 w-24 mb-1 skeleton-loading rounded"></div>
              <!-- 职位公司骨架 -->
              <div class="flex items-center gap-1">
                <div class="w-4 h-4 skeleton-loading rounded"></div>
                <div class="h-3 w-32 skeleton-loading rounded"></div>
              </div>
            </div>
          </div>

          <!-- 描述文本骨架 -->
          <div class="w-full ml-1 space-y-2">
            <div class="h-4 w-full skeleton-loading rounded"></div>
            <div class="h-4 w-3/4 skeleton-loading rounded"></div>
          </div>
        </div>

        <!-- 底部操作按钮骨架 -->
        <div class="action-section flex justify-between items-center">
          <div class="flex gap-2">
            <!-- 邮件按钮骨架 -->
            <div class="w-10 h-10 rounded-2 skeleton-loading"></div>
            <!-- ID卡片按钮骨架 -->
            <div class="w-10 h-10 rounded-2 skeleton-loading"></div>
          </div>
          <!-- 更多按钮骨架 -->
          <div class="w-10 h-10 rounded-2 skeleton-loading"></div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 mt-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="text-red-600 dark:text-red-400 mr-3">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div>
            <h3 class="text-red-800 dark:text-red-200 font-medium">Failed to load favorites</h3>
            <p class="text-red-600 dark:text-red-400 text-sm mt-1">{{ error }}</p>
          </div>
        </div>
        <button
          @click="loadFavorites"
          class="px-4 py-2 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && data.length === 0" class="text-center py-12 px-6">
      <div class="text-gray-400 dark:text-gray-500 mb-4">
        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No favorites yet</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-4">Start building your talent collection by favoriting candidates from search results.</p>
    </div>

    <!-- Folders and cards -->
    <DragFolderCard v-else v-model="mainList" class="mt-4" @move-to-folder="handleDragMoveToFolder" @reorder-items="handleReorderItems">
      <template #default="{ item }">
        <div
          v-if="item.type === 'folder'"
          draggable="true"
          class="relative folder-box rounded-lg p-5 cursor-pointer h-[215px] transition-all duration-150 text-gray-900 dark:(text-white)"
        >
          <!-- Folder菜单 -->
          <div class="absolute top-6 right-2 z-20">
            <button
              @click.stop="toggleMenu(item)"
              class="w-6 h-6 text-gray-500 hover:text-black dark:hover:text-white dark:text-gray-400 fx-cer justify-center bg-transparent"
            >
              <img src="~/assets/image/More.svg" alt="" />
            </button>
            <div
              v-if="item.showMenu"
              class="absolute right-0 mt-1 rounded shadow z-30 w-24 px-0 py-1 bg-white border border-gray-300 dark:(bg-gray-700 border-gray-600)"
            >
              <div
                @click="confirmDelete(item)"
                class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
              >
                <img src="~/assets/image/Delete.svg" alt="" />
                <span>Delete</span>
              </div>
            </div>
          </div>
          <!-- 文件夹内容缩略图 -->
          <div @click="enterFolder(item)" class="cursor-pointer mb-2 h-[120px] relative z-10 mt-5 folder-thumbnail">
            <div
                              class="grid grid-cols-3 grid-rows-2 gap-x-1 gap-y-4 justify-center overflow-hidden p-2 text-center"
              style="width: 100%; margin: 0 auto"
            >
              <div
                v-for="(user, i) in item.contents.slice(0, 6)"
                :key="i"
                class="flex items-center justify-center"
              >
                <img
                  :src="user.avatar"
                  alt="avatar"
                  class="w-12 h-12 rounded-full border border-white dark:border-gray-800 object-cover folder-avatar"
                  :title="user.name"
                />
              </div>
            </div>
          </div>

          <!-- 文件夹名（在外部已处理） -->
          <div class="mt-1 relative z-10 text-left">
            <span
              v-if="!item.editing"
              @click.stop="editFolderName(item)"
              class="cursor-pointer hover:underline text-sm"
            >
              {{ item.name || 'Unnamed Folder' }}
            </span>
            <input
              v-else
              v-model="item.name"
              @blur="onFolderInputBlur(item)"
              @keydown.enter="onFolderInputEnter(item, $event)"
              @keydown.esc="cancelFolderRename(item)"
              class="text-sm border rounded w-full px-2 py-1 bg-white text-gray-900 border-gray-300 dark:(bg-gray-700 text-white border-gray-500) folder-rename-input"
            />
          </div>
        </div>

        <div
          v-else
          class="relative flex flex-col justify-between rounded-lg h-[215px] p-4 cursor-move transition-all duration-150 hover:shadow-md hover:border-[#CB7C5D] border border-[#D6D6D6] bg-white text-gray-900 dark:(bg-[#242425] border-[#494949] text-white)"
          draggable="true"
          @click="openCardModal(item)"
        >
          <!-- Search角标 -->
          <div class="absolute top-0 left-0 px-3 py-[2px] rounded-tl-lg rounded-br-lg font-normal search-badge">
            Search
          </div>
          <!-- favorites -->
          <button
            class="absolute right-2 top-2 w-8 h-8 rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
            @click.stop="toggleFavorite(item)"
          >
            <img
              :src="item.isFavorited ? starFill2 : starEmpty2"
              alt="favorites"
              class="star-icon btn-icon-dark"
            />
            <img
              :src="item.isFavorited ? starFill : starEmpty"
              alt="favorites"
              class="star-icon btn-icon-light"
            />
          </button>
          <div class="flex flex-col gap-3 items-start mt-2">
            <div class="flex items-center gap-2 justify-start w-full">
                              <img :src="item.avatar" alt="avatar" class="w-12 h-12 rounded-full object-cover talent-card-avatar" />
              <div class="text-left text-sm max-w-[180px]">
                <p class="text-4 font-600 truncate">{{ item.name }}</p>
                <p class="fx-cer gap-1">
                  <img src="~/assets/svg/verified.svg" alt="" />
                  <span class="text-3 font-400 job-info-text truncate"
                    >{{ item.title }} , {{ item.company }}</span
                  >
                </p>
              </div>
            </div>
            <div class="text-[16px] text-[#3c3c3c] dark:text-gray-300 max-w-[240px] line-clamp-2 ml-1">
              <span v-html="item.summary || ''"></span><template v-if="item.summary && item.experience && item.experience !== '0 yrs exp'"> | </template><template v-if="item.experience && item.experience !== '0 yrs exp'">{{ item.experience }}</template>
            </div>
          </div>
          <div class="action-section">
            <div class="fx-cer gap-2">
              <!-- email -->
              <button
                class="w-10 h-10 border-1 border-black rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                @click.stop="openEmailModal(item.profileId)"
              >
                <img src="~/assets/image/email2.svg" alt="Email" class="btn-icon btn-icon-dark" />
                <img src="~/assets/image/email.svg" alt="Email" class="btn-icon btn-icon-light" />
              </button>
              <!-- id -->
              <button
                class="w-10 h-10 border-1 border-gray-300 rounded-2 fx-cer justify-center bg-transparent dark:border-gray-600 opacity-50 cursor-not-allowed relative group"
                @click="() => {}"
                disabled
              >
                <img
                  src="~/assets/image/id-card 3.svg"
                  alt="ID Card"
                  class="btn-icon btn-icon-light opacity-50"
                />
                <img
                  src="~/assets/image/id-card-dark.svg"
                  alt="ID Card"
                  class="btn-icon btn-icon-dark opacity-50"
                />
                <!-- Tooltip -->
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  Coming Soon
                </div>
              </button>
            </div>
            <!-- 卡片下拉菜单 -->
            <button
              ref="dropdownTrigger"
              class="relative w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
              @click.stop="toggleDropdownMenu(item)"
            >
              <img src="~/assets/image/More.svg" alt="" />
              <div
                v-if="item.showDropdown"
                class="absolute right-0 top-10 mt-1 rounded shadow z-30 w-32 px-0 py-1 bg-white border border-gray-300 dark:(bg-gray-700 border-gray-600)"
              >
                <div
                  class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
                  @click="openMoveToModal(item)"
                >
                  <img src="~/assets/image/Folder-close.svg" alt="" />
                  <span>Move to</span>
                </div>
                <div
                  class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
                  @click="confirmDeleteCard(item)"
                >
                  <img src="~/assets/image/Delete.svg" alt="" />
                  <span>Delete</span>
                </div>
              </div>
            </button>
          </div>
        </div>
      </template>
    </DragFolderCard>

    <!-- 删除模态框 -->
    <div
      v-if="showDeleteModal"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
    >
      <div
        class="rounded-2 shadow-lg w-80 relative p-4 bg-white text-gray-900 dark:(bg-gray-800 text-white)"
      >
        <button
          class="absolute top-2 right-2 text-gray-500 bg-transparent hover:text-gray-700 dark:hover:text-gray-300"
          @click="closeModal"
        >
          <img src="~/assets/image/Close-small.svg" alt="" />
        </button>
        <div v-if="deleteError" class="fx-cer justify-center mb-4">
          <img src="~/assets/image/warn.svg" alt="" class="w-9 h-9" />
        </div>
        <h3 class="text-3.5 font-semibold mb-4 text-center">
          {{ 
            deleteError 
              ? `Unable to delete the ${cardToDelete ? 'candidate' : 'folder'}` 
              : `Delete the ${cardToDelete ? 'candidate' : 'folder'}` 
          }}
        </h3>
        <p class="text-sm mb-4 font-400 text-center mt-3">
          {{
            deleteError
              ? deleteError
              : cardToDelete 
                ? `Are you sure you want to remove "${cardToDelete.name}" from your favorites? This action cannot be undone.`
                : `Are you sure you want to delete the "${folderToDelete?.name || 'Candidate'}" folder? This action will permanently delete the folder and its contents, and it cannot be undone.`
          }}
        </p>
        <div v-if="deleteError" class="text-center">
          <button
            class="mt-2 px-4 py-1 text-sm bg-black text-white rounded hover:bg-gray-900 w-full"
            @click="closeModal"
          >
            OK
          </button>
        </div>
        <div v-else class="flex justify-between space-x-2">
          <button
            class="px-4 py-1 flex-1 text-sm rounded bg-gray-200 text-gray-900 hover:bg-gray-300 dark:(bg-gray-600 text-white hover:bg-gray-700)"
            @click="closeModal"
          >
            Cancel
          </button>
          <button
            class="px-4 py-1 flex-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            @click="onConfirmDelete"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <!-- Move To Modal -->
    <Transition name="modal">
      <div
        v-if="moveToModalVisible"
        class="fixed inset-0 bg-black bg-opacity-30 flex items-end md:items-center justify-center z-50"
        @click.self="closeMoveToModal"
      >
        <div
          class="w-full md:w-[520px] max-h-[90vh] md:h-[470px] rounded-t-2 md:rounded-2 shadow-lg bg-[#F5F5F5] text-gray-900 dark:(bg-[#141415] text-white) relative p-4 md:p-6"
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Move to</h3>
            <button
              class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 bg-transparent"
              @click="closeMoveToModal"
            >
              <img src="~/assets/image/Close-small.svg" alt="" />
            </button>
          </div>

          <!-- Scrollable list -->
          <ul
            class="overflow-y-auto w-full max-h-[calc(100vh-220px)] border border-[#ccc] md:h-[320px] bg-white rounded-2 dark:bg-[#1E1E1E] dark:border-[#1E1E1E]"
          >
            <li
              class="px-4 py-1 h-12 fx-cer gap-4 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              :class="{
                'bg-gray-300 text-black': selectedFolder?.id === talentFavoritesNode.id,
              }"
              @click="handleFolderClick(talentFavoritesNode)"
            >
              <img
                src="~/assets/image/star.png"
                alt=""
                class="w-8 h-8 border border-[#DDD] rounded btn-icon-light"
              />
              <img
                src="~/assets/image/Vector.svg"
                alt=""
                class="w-8 h-8 p-2 border border-[#3D3F40] rounded btn-icon-dark dark:bg-[#3D3F40]"
              />
              <span>Talents</span>
            </li>
            <li
              v-for="folder in data.filter(f => f.type === 'folder')"
              :key="folder.id"
              class="px-10 py-1 h-12 fx-cer gap-4 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              :class="{
                'bg-gray-300 text-black': selectedFolder?.id === folder.id,
              }"
              @click="handleFolderClick(folder)"
            >
              <img src="~/assets/image/folder 1.svg" alt="" class="btn-icon-light" />
              <img src="~/assets/image/folder2.svg" alt="" class="btn-icon-dark" />
              <span>{{ folder.name }}</span>
            </li>
          </ul>

          <!-- Footer -->
          <div class="flex justify-between mt-4 gap-4">
            <button
              class="h-10 px-4 py-1 flex-1 text-sm rounded bg-white border border-gray-300 text-gray-900 hover:bg-gray-300 dark:(bg-transparent text-white hover:bg-gray-700 border-[#252525])"
              @click="closeMoveToModal"
            >
              Cancel
            </button>
            <button
              class="h-10 px-4 py-1 flex-1 text-sm rounded bg-black text-white hover:bg-black-300 dark:(bg-[#3B3B3B] text-white hover:bg-gray-700)"
              @click="moveItemToFolder(folder)"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </Transition>
    <Notification ref="notifier" />
    <!-- 邮箱模态框 -->
    <EmailModal v-model="showEmailModal" :user-id="currentUserId" />
    <!-- 卡片详情模态框 -->
    <CardDetailModal v-model="showCardModal" :user="selectedUser" />
  </div>
</template>

<script setup>
  import { reactive, ref, computed, defineProps, onMounted, onBeforeUnmount, nextTick } from 'vue'
  import DragFolderCard from './DragFolderCard.vue'
  import starFill from '~/assets/image/star-fill.svg'
  import starEmpty from '~/assets/image/star.png'
  import starFill2 from '~/assets/image/star-fill2.svg'
  import starEmpty2 from '~/assets/image/Star-dark.svg'
  import Notification from '@/components/SearchCard/Notification.vue'
  import EmailModal from '@/components/SearchCard/EmailModal.vue'
  import CardDetailModal from './CardDetailModal.vue'
  // 移除favoriteApi导入，直接使用fetch调用代理API

  const currentFolder = ref(null)
  const showDeleteModal = ref(false)
  const folderToDelete = ref(null)
  const cardToDelete = ref(null)
  const deleteError = ref('')
  const moveToModalVisible = ref(false)
  const selectedCardForMove = ref(null)
  const dropdownTrigger = ref([])
  const showCardModal = ref(false)
  const selectedUser = ref(null)
  const selectedFolder = ref(null)
  const notifier = ref()
  const isMobile = ref(false)

  // API相关状态
  const loading = ref(false)
  const error = ref('')
  const { currentUser } = useFirebaseAuth()

  // API基础URL - 使用代理路径
  const BASE_URL = '/api/v1/favorite'

  // 通用API请求函数 - 参考FavoriteApiTest.vue的实现
  const makeRequest = async (method, endpoint, data = null) => {
    if (!currentUser.value?.uid) {
      throw new Error('User not authenticated')
    }

    const config = {
      method,
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`,
        'Content-Type': 'application/json'
      }
    }

    if (data && (method === 'POST' || method === 'PATCH')) {
      config.body = JSON.stringify(data)
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, config)
    const result = await response.json()

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${JSON.stringify(result)}`)
    }

    return result
  }

  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }

  const props = defineProps({
    onGoToUpload: {
      type: Function,
      required: true,
    },
  })

  // 选中文件夹
  const handleFolderClick = folder => {
    selectedFolder.value = folder
  }

  const notify = message => {
    notifier.value?.add(message)
  }

  // 卡片详情
  function openCardModal(user) {
    selectedUser.value = user
    showCardModal.value = true
  }

  let idCounter = 1000
  const genId = () => idCounter++

  const talentFavoritesNode = {
    id: -1,
    type: 'folder',
    name: 'Talent favorites',
    isTalentFavorites: true, // 标记为顶级节点
  }

  // 数据转换函数：将API数据转换为页面需要的格式
  const transformApiDataToPageFormat = (apiData) => {
    const result = []

    // 转换人才卡片数据的函数
    const transformCard = (item) => ({
      id: item.id,
      type: 'card',
      name: item.profile?.name || 'Unknown',
      avatar: item.profile?.avatar_url || `https://i.pravatar.cc/40?u=${item.profile?.id || item.id}`,
      isFavorited: true, // 收藏页面中的都是已收藏的
      title: item.profile?.research_areas?.[0] || 'Researcher',
      tags: item.profile?.research_areas || ['Research'],
      summary: item.profile?.summary?.description || '',
      experience: item.profile?.exp_years && item.profile.exp_years > 0 ? `${item.profile.exp_years} yrs exp` : '',
      company: item.profile?.company || 'Academic',
      apiId: item.id, // 保存API中的ID
      profileId: item.profile?.id,
      parentId: item.parent_id,
      order: item.order,
      showDropdown: false,
      // 添加 Network 按钮检测所需的字段
      profile: item.profile, // 保留完整的 profile 数据
      dataset: item.profile?.dataset, // 从 profile 中获取 dataset
      group: item.profile?.group // 从 profile 中获取 group
    })

    // 处理API数据
    apiData.forEach(item => {
      if (item.is_folder) {
        // 处理文件夹
        const folderData = {
          id: item.id,
          type: 'folder',
          name: item.folder_name || 'Unnamed Folder',
          contents: reactive([]),
          editing: false,
          showMenu: false,
          apiId: item.id, // 保存API中的ID
          parentId: item.parent_id,
          order: item.order
        }

        // 处理文件夹中的children
        if (item.children && Array.isArray(item.children)) {
          const childCards = item.children
            .filter(child => !child.is_folder) // 只处理人才卡片，不处理嵌套文件夹
            .map(transformCard)
            .sort((a, b) => (a.order || 0) - (b.order || 0))

          folderData.contents.push(...childCards)
        }

        // 只添加根级别的文件夹
        if (!item.parent_id || item.parent_id === '00000000-0000-0000-0000-000000000000') {
          result.push(folderData)
        }
      } else {
        // 处理根级别的人才卡片（不在文件夹中的）
        if (!item.parent_id || item.parent_id === '00000000-0000-0000-0000-000000000000') {
          result.push(transformCard(item))
        }
      }
    })

    // 按order排序
    result.sort((a, b) => (a.order || 0) - (b.order || 0))

    return result
  }

  // 使用响应式数据存储收藏列表
  const data = reactive([])

  // 加载收藏数据
  const loadFavorites = async () => {
    if (!currentUser.value?.uid) {
      console.warn('User not authenticated, cannot load favorites')
      return
    }

    loading.value = true
    error.value = ''

    try {
      const response = await makeRequest('GET', '/list?filter=all')
      console.log('API Response:', response) // 调试日志
      if (response.data && Array.isArray(response.data)) {
        const transformedData = transformApiDataToPageFormat(response.data)
        console.log('Transformed Data:', transformedData) // 调试日志
        // 清空现有数据并添加新数据
        data.splice(0, data.length, ...transformedData)
      }
    } catch (err) {
      console.error('Failed to load favorites:', err)
      error.value = err.message || 'Failed to load favorites'
      notify('Failed to load favorites')
    } finally {
      loading.value = false
    }
  }

  const mainList = computed({
    get() {
      return currentFolder.value ? currentFolder.value.contents : data
    },
    set(val) {
      if (currentFolder.value) {
        currentFolder.value.contents = val
      } else {
        data.splice(0, data.length, ...val)
      }
    },
  })

  async function addFolder() {
    if (!currentUser.value?.uid) {
      notify('Please login to create folders')
      return
    }

    const baseName = 'New Folder'
    let maxIndex = 0

    // 遍历现有文件夹名称
    data.forEach(item => {
      if (item.type === 'folder') {
        const name = item.name?.trim()

        if (name === baseName) {
          maxIndex = Math.max(maxIndex, 1)
        } else {
          const match = name.match(/^New Folder (\d+)$/)
          if (match) {
            const num = parseInt(match[1])
            if (!isNaN(num)) {
              maxIndex = Math.max(maxIndex, num)
            }
          }
        }
      }
    })

    const nextName = maxIndex === 0 ? baseName : `${baseName} ${maxIndex + 1}`

    try {
      // 调用API创建文件夹
      const response = await makeRequest('POST', '/folder', {
        parent_id: null
      })

      if (response.data) {
        const newFolder = {
          id: response.data.id,
          type: 'folder',
          name: nextName,
          contents: reactive([]),
          editing: false,
          showMenu: false,
          apiId: response.data.id,
          parentId: response.data.parent_id,
          order: response.data.order
        }

        // 立即重命名文件夹为生成的名称
        await makeRequest('POST', '/folder/rename', {
          folder_id: response.data.id,
          name: nextName
        })
        newFolder.name = nextName

        data.unshift(newFolder)
        notify('Folder created successfully')
      }
    } catch (err) {
      console.error('Failed to create folder:', err)
      notify('Failed to create folder')
    }
  }

  function editFolderName(folder) {
    // 保存原始名称用于取消时恢复
    folder.originalName = folder.name
    folder.editing = true
    
    // 下一帧自动聚焦输入框
    nextTick(() => {
      // 更精确地找到对应的输入框
      const inputs = document.querySelectorAll('.folder-rename-input')
      for (const input of inputs) {
        if (input.value === folder.name) {
          input.focus()
          // 使用setTimeout确保input已完全渲染
          setTimeout(() => {
            input.select() // 选中所有文本
          }, 0)
          break
        }
      }
    })
  }

  async function saveFolderName(folder) {
    folder.editing = false
    const newName = folder.name.trim()

    if (!newName) {
      folder.name = 'Unnamed Folder'
      return
    }

    if (!currentUser.value?.uid) {
      notify('Please login to rename folders')
      return
    }

    try {
      // 调用API重命名文件夹
      await makeRequest('POST', '/folder/rename', {
        folder_id: folder.apiId || folder.id,
        name: newName
      })
      folder.name = newName
      notify('Folder renamed successfully')
    } catch (err) {
      console.error('Failed to rename folder:', err)
      notify('Failed to rename folder')
      // 可以考虑恢复原名称
    }
  }

  function cancelFolderRename(folder) {
    folder.editing = false
    // 恢复原始名称
    if (folder.originalName) {
      folder.name = folder.originalName
      delete folder.originalName
    }
  }

  // 处理回车键事件
  function onFolderInputEnter(folder, event) {
    event.preventDefault()
    event.target.blur() // 触发blur，由blur处理保存
  }

  // 处理失焦事件
  function onFolderInputBlur(folder) {
    // 防止重复调用
    if (!folder.editing) return
    saveFolderName(folder)
  }

  function enterFolder(folder) {
    currentFolder.value = folder
  }
  function exitFolder() {
    currentFolder.value = null
  }
  function toggleMenu(folder) {
    folder.showMenu = !folder.showMenu
  }

  function removeFromAll(item) {
    const id = item.id

    // 从根目录找
    const idx = data.findIndex(i => i.id === id)
    if (idx !== -1) return data.splice(idx, 1)

    // 从所有文件夹里找
    for (const folder of data.filter(f => f.type === 'folder')) {
      const i = folder.contents.findIndex(c => c.id === id)
      if (i !== -1) return folder.contents.splice(i, 1)
    }
  }

  function confirmDelete(folder) {
    folder.showMenu = false
    folderToDelete.value = folder
    deleteError.value = ''
    showDeleteModal.value = true
  }

  function closeModal() {
    showDeleteModal.value = false
    deleteError.value = ''
    folderToDelete.value = null
    cardToDelete.value = null
  }

  // 删除文件夹
  async function onConfirmDelete() {
    // 如果是删除文件夹
    if (folderToDelete.value) {
      if (folderToDelete.value.contents.length > 0) {
        deleteError.value =
          'The folder contains candidates and cannot be deleted. Please remove or delete all candidates from the folder before trying again.'
        return
      }

      if (!currentUser.value?.uid) {
        deleteError.value = 'Please login to delete folders'
        return
      }

      try {
        // 调用API删除文件夹
        await makeRequest('DELETE', `/folder/${folderToDelete.value.apiId || folderToDelete.value.id}`)

        const index = data.findIndex(f => f.id === folderToDelete.value.id)
        if (index !== -1) data.splice(index, 1)
        showDeleteModal.value = false
        if (currentFolder.value?.id === folderToDelete.value?.id) {
          currentFolder.value = null
        }
        folderToDelete.value = null
        notify('Folder deleted successfully')
      } catch (err) {
        console.error('Failed to delete folder:', err)
        deleteError.value = err.message || 'Failed to delete folder'
      }
    }
    // 如果是删除卡片
    else if (cardToDelete.value) {
      if (!currentUser.value?.uid) {
        deleteError.value = 'Please login to delete candidates'
        return
      }

      try {
        // 调用API删除收藏
        await makeRequest('DELETE', `/remove/${cardToDelete.value.apiId || cardToDelete.value.id}`)

        // 从UI中移除该卡片
        removeFromAll(cardToDelete.value)
        showDeleteModal.value = false
        cardToDelete.value = null
        notify('Candidate deleted successfully')
      } catch (err) {
        console.error('Failed to delete candidate:', err)
        deleteError.value = err.message || 'Failed to delete candidate'
      }
    }
  }

  // 确认删除卡片
  function confirmDeleteCard(card) {
    card.showDropdown = false
    cardToDelete.value = card
    folderToDelete.value = null // 确保文件夹删除变量为空
    deleteError.value = ''
    showDeleteModal.value = true
  }

  function toggleDropdownMenu(card) {
    const isOpen = card.showDropdown
    // 首先关闭所有其他卡片的菜单
    data.forEach(item => {
      if (item.type === 'folder') {
        item.contents.forEach(c => (c.showDropdown = false))
      }
      if (item.type === 'card') {
        item.showDropdown = false
      }
    })

    // 如果之前是关闭状态，则打开；如果已经是打开状态，就关闭（不再重新打开）
    if (!isOpen) {
      card.showDropdown = true
      selectedCardForMove.value = card
    } else {
      selectedCardForMove.value = null
    }
  }

  function openMoveToModal(card) {
    moveToModalVisible.value = true
    selectedCardForMove.value = card
    card.showDropdown = false // 关闭菜单
  }

  // 关闭移动到模态框
  function closeMoveToModal() {
    moveToModalVisible.value = false
    selectedFolder.value = null

    data.forEach(item => {
      if (item.type === 'card' && item.showDropdown) {
        item.showDropdown = false
      }
      if (item.type === 'folder') {
        item.contents.forEach(c => {
          if (c.type === 'card' && c.showDropdown) {
            c.showDropdown = false
          }
        })
      }
    })
  }

  async function moveItemToFolder() {
    if (!selectedCardForMove.value) return

    if (!currentUser.value?.uid) {
      notify('Please login to move items')
      return
    }

    try {
      const targetFolderId = selectedFolder.value.isTalentFavorites
        ? null
        : (selectedFolder.value.apiId || selectedFolder.value.id)

      // 调用API移动卡片
      await makeRequest('PATCH', '/folder', {
        favorite_id: selectedCardForMove.value.apiId || selectedCardForMove.value.id,
        folder_id: targetFolderId
      })

      // 从当前所在位置移除卡片
      removeFromAll(selectedCardForMove.value)

      if (selectedFolder.value.isTalentFavorites) {
        // 插入到根级数据顶部
        data.unshift({ ...selectedCardForMove.value })
      } else {
        // 插入目标文件夹
        selectedFolder.value.contents.push({ ...selectedCardForMove.value })
      }

      closeMoveToModal()
      notify('Item moved successfully')
    } catch (err) {
      console.error('Failed to move item:', err)
      notify('Failed to move item')
    }
  }

  // 处理拖拽移动到文件夹的事件
  async function handleDragMoveToFolder({ card, targetFolder }) {
    if (!currentUser.value?.uid) {
      notify('Please login to move items')
      return
    }

    // 保存原始状态用于回滚
    const originalParentId = card.parentId
    let originalLocation = null
    let originalIndex = -1

    // 找到卡片的原始位置
    const rootIndex = data.findIndex(i => i.id === card.id)
    if (rootIndex !== -1) {
      originalLocation = 'root'
      originalIndex = rootIndex
    } else {
      // 查找在哪个文件夹中
      for (const folder of data.filter(f => f.type === 'folder')) {
        const index = folder.contents.findIndex(c => c.id === card.id)
        if (index !== -1) {
          originalLocation = folder
          originalIndex = index
          break
        }
      }
    }

    try {
      // 🚀 乐观更新：立即更新UI
      // 从当前所在位置移除卡片
      removeFromAll(card)

      // 确保目标文件夹的contents是响应式的
      if (!targetFolder.contents) {
        targetFolder.contents = reactive([])
      }

      // 添加到目标文件夹
      targetFolder.contents.push(card)
      
      // 更新卡片的parentId
      card.parentId = targetFolder.apiId || targetFolder.id

      // 后台调用API移动卡片
      await makeRequest('PATCH', '/folder', {
        favorite_id: card.apiId || card.id,
        folder_id: targetFolder.apiId || targetFolder.id
      })

      // 静默通知（可选）
      // notify('Item moved successfully')
    } catch (err) {
      console.error('Failed to move item:', err)
      notify('Failed to move item')

      // 🔄 回滚：恢复原始状态
      // 从目标文件夹移除
      const targetIndex = targetFolder.contents.findIndex(i => i.id === card.id)
      if (targetIndex !== -1) {
        targetFolder.contents.splice(targetIndex, 1)
      }

      // 恢复到原始位置
      card.parentId = originalParentId
      if (originalLocation === 'root') {
        data.splice(originalIndex, 0, card)
      } else if (originalLocation && originalLocation.contents) {
        originalLocation.contents.splice(originalIndex, 0, card)
      }

      // 或者重新加载数据以确保一致性
      // loadFavorites()
    }
  }

  // 处理拖拽排序的事件
  async function handleReorderItems({ draggedItem, fromIndex, targetIndex }) {
    if (!currentUser.value?.uid) {
      notify('Please login to reorder items')
      return
    }

    // 获取当前列表的引用
    const currentList = currentFolder.value ? currentFolder.value.contents : data
    
    // 保存原始状态用于回滚
    const originalList = [...currentList]
    const originalOrder = draggedItem.order

    try {
      // 🚀 乐观更新：立即更新UI
      const item = currentList.splice(fromIndex, 1)[0]
      currentList.splice(targetIndex, 0, item)

      // 直接使用目标索引作为新的order值
      const newOrder = targetIndex

      console.log('Reordering item (optimistic):', {
        draggedItem: draggedItem.name,
        fromIndex,
        targetIndex,
        newOrder
      })

      // 后台调用API更新排序
      await makeRequest('POST', '/reorder', {
        favorite_id: draggedItem.apiId || draggedItem.id,
        order: newOrder
      })

      // API成功，更新item的order属性
      draggedItem.order = newOrder
      
      // 静默通知（可选）
      // notify('Item reordered successfully')
    } catch (err) {
      console.error('Failed to reorder item:', err)
      notify('Failed to reorder item')

      // 🔄 回滚：恢复原始状态
      currentList.splice(0, currentList.length, ...originalList)
      draggedItem.order = originalOrder

      // 或者重新加载数据以确保一致性
      // loadFavorites()
    }
  }
  // 修改关注状态 - 在收藏页面中，这个操作是移除收藏
  function toggleFavorite(card) {
    if (!currentUser.value?.uid) {
      notify('Please login to manage favorites')
      return
    }

    // 在收藏页面中，点击星标需要二次确认删除
    confirmDeleteCard(card)
  }

  const showEmailModal = ref(false)
  // 当前用户 ID
  const currentUserId = ref(null)

  // 打开email模态框
  const openEmailModal = userId => {
    currentUserId.value = userId
    showEmailModal.value = true
  }

  // 监听页面拖拽上传
  onMounted(() => {
    checkMobile()
    window.addEventListener('resize', checkMobile)

    // ========== 拖拽监听 ==========
    window.addEventListener('dragover', handleDragOver)
    window.addEventListener('drop', handleDrop)

    // 加载收藏数据
    loadFavorites()
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', checkMobile)
    window.removeEventListener('dragover', handleDragOver)
    window.removeEventListener('drop', handleDrop)
  })

  function handleDragOver(event) {
    event.preventDefault()
  }

  function handleDrop(event) {
    event.preventDefault()

    const files = event.dataTransfer?.files
    if (!files || files.length === 0) return

    const fileList = Array.from(files)
    const validFiles = fileList.filter(f => f.type.startsWith('image/') || f.type || f.name)

    if (validFiles.length > 0) {
      // 跳转到上传页
      props.onGoToUpload()

      // ✅ 延迟一点时间等页面切换完再上传
      setTimeout(() => {
        // 使用全局事件或 Pinia 等传递 fileList
        window.dispatchEvent(
          new CustomEvent('start-upload', {
            detail: validFiles,
          })
        )
      }, 300)
    }
  }
</script>

<style scoped>
  .action-section {
    display: flex;
    width: 100%;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
  }

  .btn-icon {
    width: 20px;
    height: 20px;
  }

  /* 星星图标特殊尺寸 */
  .star-icon {
    width: 24px;
    height: 24px;
  }

  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  /* 文件夹 */
  .folder-box {
    color: #333;
    border-radius: 10px;
    background-image: url('~/assets/image/folder-w1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;

    z-index: 0;
  }

  .folder-box:hover {
    background-image: url('~/assets/image/folder-w2.png');
    background-size: 104% 107%;
    background-position: -6px -5px;
    background-repeat: no-repeat;
    border-radius: 12px;
  }

  .dark .folder-box {
    background-image: url('~/assets/image/folder-d1.png');
  }

  .dark .folder-box:hover {
    background-image: url('~/assets/image/folder-d2.png');
    background-size: 104% 107%;
    background-position: -6px -5px;
    background-repeat: no-repeat;
    border-radius: 12px;
  }

  @media (max-width: 768px) {
    .talent-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap; /* 保证空间不足时换行 */
      gap: 0.5rem;
    }

    .talent-title > div:first-child {
      margin-bottom: 0 !important; /* 移除标题与按钮之间的间距 */
    }

    .talent-title > .fx-cer {
      margin-bottom: 0;
    }
  }

  @media (max-width: 360px) {
    .talent-title {
      flex-direction: column;
      align-items: flex-start;
    }

    .talent-title > .fx-cer {
      width: 100%;
      flex-wrap: wrap;
      gap: 0.5rem;
    }
  }

  /* 搜索角标样式 */
  .search-badge {
    background-color: #E7F8ED;
    color: #35B063;
    font-size: 10px;
  }

  .dark .search-badge {
    background-color: #253B2D;
    color: #429A63;
  }

  /* 职位信息文字颜色 */
  .job-info-text {
    color: #666666;
  }

  .dark .job-info-text {
    color: #7A7A7A;
  }

  /* 文件夹拖拽优化 - 防止拖拽内部元素 */
  .folder-box[draggable="true"] .folder-thumbnail *,
  .folder-box[draggable="true"] .folder-avatar {
    pointer-events: none;
    user-select: none;
    -webkit-user-drag: none;
  }

  /* 确保文件夹本身可以被拖拽 */
  .folder-box[draggable="true"] {
    -webkit-user-drag: element;
  }

  /* 禁用人才卡片内部元素的拖拽 */
  .talent-card-avatar {
    pointer-events: none;
    user-select: none;
    -webkit-user-drag: none;
  }

  /* 文件夹重命名输入框样式 */
  .folder-rename-input {
    outline: none;
  }

  .folder-rename-input:focus {
    border-color: #CB7C5D;
    box-shadow: 0 0 0 2px rgba(203, 124, 93, 0.2);
  }

  .folder-rename-input::selection {
    background-color: #CB7C5D;
    color: white;
  }

  .folder-rename-input::-moz-selection {
    background-color: #CB7C5D;
    color: white;
  }

  /* 骨架屏网格布局 - 匹配DragFolderCard的响应式网格 */
  .skeleton-grid {
    display: grid;
    scroll-behavior: smooth;

    /* 滚动区域的内边距 */
    padding: 16px 24px 48px 24px;

    /* 移动端: 单列，295px卡片，12px间距 */
    grid-template-columns: 295px;
    gap: 12px;
    justify-content: start;

    /* 小平板端: 2列自适应，295-310px卡片，16px间距 */
    @media (min-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(295px, 310px));
      gap: 16px;
      justify-content: start;
    }

    /* 中等平板: 2-3列自适应，300-315px卡片，22px间距 */
    @media (min-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(300px, 315px));
      gap: 22px;
      justify-content: start;
    }

    /* 桌面端: 自动计算列数，305-320px卡片，24px间距 */
    @media (min-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(305px, 320px));
      gap: 24px;
      justify-content: start;
    }

    /* 大屏桌面: 310-325px卡片，28px间距 */
    @media (min-width: 1400px) {
      grid-template-columns: repeat(auto-fit, minmax(310px, 325px));
      gap: 28px;
      justify-content: start;
    }

    /* 超大屏: 最大间距32px */
    @media (min-width: 1800px) {
      gap: 32px;
    }
  }

  /* 骨架屏卡片样式 */
  .skeleton-card {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 8px;
    height: 215px;
    padding: 16px;
    border: 1px solid #D6D6D6;
    background-color: white;
    min-height: 215px;
    width: 100%;
    max-width: 325px;
    min-width: 295px;
    opacity: 0;
    animation: skeleton-fade-in 0.6s ease-out forwards;
  }

  .dark .skeleton-card {
    background-color: #242425;
    border-color: #494949;
  }

  /* 骨架屏加载动画 */
  .skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading-animation 1.5s infinite;
  }

  .dark .skeleton-loading {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }

  @keyframes skeleton-loading-animation {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  @keyframes skeleton-fade-in {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
