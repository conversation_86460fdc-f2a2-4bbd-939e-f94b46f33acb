<template>
  <Transition name="modal">
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 bg-black bg-opacity-40 flex justify-center items-end md:items-center"
      @click.self="$emit('update:modelValue', false)"
    >
      <!-- 主体弹窗 -->
      <div
        class="card-detail-modal relative p-4 shadow-lg w-full md:w-[320px] md:mx-0 fixed md:relative bottom-0 md:bottom-auto rounded-t-xl md:rounded-xl transition-transform duration-300 md:rounded-xl rounded-b-none md:px-4"
      >
        <!-- 关闭按钮 -->
        <button
          class="absolute top-2 right-2 text-gray-500 bg-transparent hover:text-gray-700 dark:hover:text-gray-300"
          @click="$emit('update:modelValue', false)"
        >
          <img src="~/assets/image/Close-small.svg" alt="" />
        </button>

        <!-- 顶部用户信息 -->
        <div class="flex items-center gap-3 mb-4">
          <img :src="user.avatar" alt="avatar" class="w-12 h-12 rounded-full object-cover" />
          <div class="text-left">
            <p class="text-base font-semibold">{{ user.name }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.title }}{{ user.company ? ', ' + user.company : '' }}</p>
          </div>
        </div>

        <!-- 下方按钮区域（竖排） -->
        <div class="flex flex-col gap-3">
          <!-- 动态显示的分析按钮 -->
          <div v-if="availableAnalyzeOptions.length > 0" class="flex flex-col gap-2">
            <button
              v-if="isGitHubEnabled"
              class="btn-analyze"
              @click="handleGitHubAnalyze"
            >
              <img src="~/assets/image/github2.svg" alt="GitHub" class="btn-icon btn-icon-light" />
              <img src="~/assets/image/github3.svg" alt="GitHub" class="btn-icon btn-icon-dark" />
              Analyze GitHub
            </button>
            <button
              v-if="isScholarEnabled"
              class="btn-analyze"
              @click="handleScholarAnalyze"
            >
              <img src="~/assets/image/mortarboard1.svg" alt="Scholar" class="btn-icon btn-icon-light" />
              <img src="~/assets/image/mortarboard2.svg" alt="Scholar" class="btn-icon btn-icon-dark" />
              Analyze Scholar
            </button>
            <button
              v-if="isLinkedInEnabled"
              class="btn-analyze"
              @click="handleLinkedInAnalyze"
            >
              <img src="~/assets/image/search-LinkedIn.svg" alt="LinkedIn" class="btn-icon btn-icon-light" />
              <img src="~/assets/image/search-LinkedIn-dark.svg" alt="LinkedIn" class="btn-icon btn-icon-dark" />
              Analyze LinkedIn
            </button>
          </div>

          <button
            class="btn-network"
            @click="showNetworkModal = true"
            :disabled="!isNetworkButtonEnabled"
            :class="{ 'btn-disabled cursor-not-allowed': !isNetworkButtonEnabled }"
          >
            <img
              src="~/assets/image/button-group.svg"
              alt="Network"
              :class="['btn-icon btn-icon-dark', { 'opacity-50': !isNetworkButtonEnabled }]"
            />
            <img
              src="~/assets/image/button-group2.svg"
              alt="Network"
              :class="['btn-icon btn-icon-light', { 'opacity-50': !isNetworkButtonEnabled }]"
            />
            Network
          </button>
          <button class="btn-see-another btn-disabled cursor-not-allowed relative group" disabled>
            <img src="~/assets/image/cv1.svg" alt="Resume/CV" class="btn-icon btn-icon-light opacity-50" />
            <img src="~/assets/image/cv2.svg" alt="Resume/CV" class="btn-icon btn-icon-dark opacity-50" />
            Resume/CV
            <!-- Tooltip -->
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Coming Soon
            </div>
          </button>
        </div>
      </div>
      <!-- Network模态框组件 -->
      <NetworkModal
        v-if="props.user"
        :show="showNetworkModal"
        :currentCandidate="props.user"
        @update:show="showNetworkModal = $event"
      />
    </div>
  </Transition>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import NetworkModal from '@/components/SearchCard/NetworkModal.vue'
  import { useRouter } from 'vue-router'
  import { extractGitHubUsername } from '~/utils'
  const router = useRouter()

  const props = defineProps({
    modelValue: Boolean,
    user: {
      type: Object,
      required: true,
    },
  })

  const showNetworkModal = ref(false)

  const emit = defineEmits(['update:modelValue'])

  // 判断各个分析选项是否可用
  const isGitHubEnabled = computed(() => {
    return props.user?.profile?.github != null
  })

  const isScholarEnabled = computed(() => {
    return props.user?.profile?.scholar != null || props.user?.profile?.openreview != null
  })

  const isLinkedInEnabled = computed(() => {
    return props.user?.profile?.linkedin != null
  })

  // 获取可用的分析选项
  const availableAnalyzeOptions = computed(() => {
    const options = []
    if (isGitHubEnabled.value) options.push('github')
    if (isScholarEnabled.value) options.push('scholar')
    if (isLinkedInEnabled.value) options.push('linkedin')
    return options
  })

  // 判断Network按钮是否可用
  const isNetworkButtonEnabled = computed(() => {
    const candidate = props.user
    if (!candidate) return false

    // 检查新的search API数据结构
    if (candidate.profile) {
      // 使用dataset字段，向后兼容builder字段
      const dataset = candidate.dataset || candidate.builder
      if (dataset === 'github') {
        // GitHub用户需要有profile.id字段
        return !!(candidate.profile.id)
      } else if (dataset === 'scholar') {
        if (candidate.group === 'company') {
          // 公司类型需要有id字段
          return !!(candidate.profile.id)
        } else {
          // Scholar用户需要有openreview字段
          return !!(candidate.profile.openreview)
        }
      }
    }

    // 兼容旧的数据结构
    if (candidate.author_ids) {
      return !!(candidate.author_ids)
    }

    return false
  })

  // --- Actions ---
  const handleGitHubAnalyze = () => {
    if (!isGitHubEnabled.value) return
    
    const githubId = props.user.profile.github
    // 使用extractGitHubUsername处理GitHub用户名，去除可能的末尾斜杠
    const processedId = extractGitHubUsername(githubId)
    const encodedId = encodeURIComponent(processedId)
    const url = `/github?user=${encodedId}`
    window.open(url, '_blank')
  }

  const handleScholarAnalyze = () => {
    if (!isScholarEnabled.value) return
    
    // 优先使用openreview，fallback到scholar
    const analyzeId = props.user.profile.openreview || props.user.profile.scholar
    const encodedId = encodeURIComponent(analyzeId)
    const url = `/scholar?user=${encodedId}`
    window.open(url, '_blank')
  }

  const handleLinkedInAnalyze = () => {
    if (!isLinkedInEnabled.value) return
    
    const linkedinId = props.user.profile.linkedin
    const encodedId = encodeURIComponent(linkedinId)
    const url = `/linkedin?user=${encodedId}`
    window.open(url, '_blank')
  }
</script>

<style>
  /* 卡片详情模态框样式 */
  .card-detail-modal {
    background-color: white;
    color: #1f2937;
  }

  .dark .card-detail-modal {
    background-color: #141415;
    color: white;
  }

  /* 按钮图标控制 */
  .btn-icon {
    width: 24px;
    height: 24px;
  }

  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  .btn-analyze,
  .btn-network,
  .btn-see-another {
    height: 40px;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    box-sizing: border-box;
    border: 1px solid #000000;
  }

  .btn-analyze,
  .btn-network {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 32px; -- width/height and flex handles this */
  }

  .btn-analyze:hover,
  .btn-network:hover {
    background-color: #ccc;
  }
  .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f5f5f5 !important;
    color: #999 !important;
  }
  .btn-disabled:hover {
    background-color: #f5f5f5 !important;
  }

  .btn-see-another {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 20px; */
  }

  .btn-see-another:hover {
    background-color: #f5f5f5;
  }

  .dark .btn-see-another,
  .dark .btn-analyze,
  .dark .btn-network {
    background-color: transparent;
    border: 1px solid #323232;
    color: #faf9f5;
  }

  .dark .btn-see-another:hover,
  .dark .btn-analyze:hover,
  .dark .btn-network:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }
  .dark .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #2a2a2a !important;
    color: #666 !important;
    border-color: #444 !important;
  }
  .dark .btn-disabled:hover {
    background-color: #2a2a2a !important;
  }

  @media (max-width: 768px) {
    .modal-enter-active,
    .modal-leave-active {
      transition: all 0.3s ease;
    }

    .modal-enter-from {
      transform: translateY(100%);
      opacity: 0;
    }

    .modal-enter-to {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-from {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
</style>
