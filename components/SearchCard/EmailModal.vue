<template>
  <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
    <div class="modal-content relative">
      <button
        class="absolute top-2 right-3 text-xl font-bold text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 bg-transparent"
        @click="closeModal"
      >
        <div class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white dark:opacity-60"></div>
      </button>
      <div class="text-5 font-600">Email</div>
      <div
        class="fx-cer justify-between gap-2 font-[#666] p-3 bg-[#FFFAEB] dark:bg-[#242424] rounded-2"
      >
        <img src="~/assets/image/info.svg" alt="" />
        <div class="text-3 text-left">
          The email address is sourced from web search or Al and may not be accurate.
        </div>
      </div>
      <div v-if="loadingEmail" class="loading-container">
        <!-- Loading -->
        <div class="custom-loader">
          <Loading />
          <p>Loading Email Information...</p>
        </div>
      </div>
      <div v-else class="email-display mt-4">
        <div class="email-text">
          <span>{{ userEmail }}</span>
          <button class="copy-btn" @click="copyToClipboard">
            <img src="~/assets/image/Copy.svg" alt="" />
          </button>
        </div>
        <button class="email-icon" @click="openSystemMail">
          <img src="~/assets/image/email2.svg" alt="Network" class="btn-icon btn-icon-dark" />
          <img src="~/assets/image/email.svg" alt="Network" class="btn-icon btn-icon-light" />
        </button>
      </div>
    </div>
    <Notification ref="notifier" position="center"/>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import Loading from './Loading.vue'
  import Notification from './Notification.vue'
  import { getEmail } from '~/api'

  const notifier = ref()
  const { currentUser } = useFirebaseAuth()

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true,
    },
    userId: {
      type: [String, Number],
      default: null,
    },
  })

  const notify = message => {
    notifier.value?.add(message)
  }

  const emit = defineEmits(['update:modelValue'])

  const loadingEmail = ref(true)
  const userEmail = ref('')

  // 真实接口获取 email
  const fetchUserEmail = async () => {
    if (!props.userId || !currentUser.value) {
      userEmail.value = 'User not authenticated or no profile ID'
      loadingEmail.value = false
      return
    }

    try {
      const params = {
        profile_id: props.userId.toString()
      }

      const response = await getEmail(params, currentUser.value.uid)

      if (response.data) {
        // 响应格式为 {"data":"<EMAIL>"}
        userEmail.value = response.data
      } else if (response.error) {
        userEmail.value = 'Email not available'
      } else {
        userEmail.value = 'No email found'
      }
    } catch (err) {
      console.error('Email fetch error:', err)
      userEmail.value = 'Email not available'
    } finally {
      loadingEmail.value = false
    }
  }

  // 打开时触发加载
  watch(
    () => props.modelValue,
    newVal => {
      if (newVal && props.userId) {
        loadingEmail.value = true
        fetchUserEmail()
      }
    },
    { immediate: true }
  )

  const closeModal = () => {
    emit('update:modelValue', false)
  }

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(userEmail.value)
      .then(() => {
        notify('Email address copied')
      })
      .catch(() => {
        notify('Email address copy failed')
      })
  }

  const openSystemMail = () => {
    window.location.href = `mailto:${userEmail.value}`
  }
</script>
<style scoped>
  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  /* email */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;
  }

  .modal-content {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 360px;
    /* height: 250px; */
    text-align: center;
  }

  .dark .modal-content {
    background: #1a1a1a;
  }

  /* Loading */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .custom-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 140px;
  }

  .custom-loader p {
    margin-top: 10px;
    font-size: 12px;
    color: #000;
  }

  .dark .custom-loader p {
    color: #fff;
  }

  .email-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .email-text {
    flex: 1;
    font-size: 14px;
    padding: 4px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fafafa;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .email-text span {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
    text-align: left;
  }

  .dark .email-text {
    background: #242424;
    border: 1px solid #4e4e4e;
  }

  .copy-btn,
  .email-icon {
    padding: 6px;
    border: none;
    background-color: transparent;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  .email-icon {
    background-color: transparent;
    border: 1px solid #ccc;
  }

  .dark .email-icon {
    background-color: transparent;
    border: 1px solid #4e4e4e;
  }
</style>
